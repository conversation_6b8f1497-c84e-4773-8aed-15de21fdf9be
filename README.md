# FreePPS 🔋⚡

**让澎湃P1/P2芯片设备享受公版PPS快充支持！**

[![GitHub release (latest by date)](https://img.shields.io/github/v/release/Seyud/FreePPS)](https://github.com/Seyud/FreePPS/releases/latest)
[![Language](https://img.shields.io/badge/Language-Rust-orange)](https://www.rust-lang.org/)

## ✨ 模块简介

FreePPS 是一个专为搭载澎湃 P1、P2 快充芯片的米系设备设计的模块，能够**解锁并启用公版 PPS（Programmable Power Supply）快充协议支持**，让你的设备享受更好的兼容性！

> ⚠️ **重要注意事项**：开启公版PPS支持后，设备将**自动关闭MIPPS（小米私有PPS协议）支持**。两种协议存在冲突，无法同时启用。请根据您的充电设备选择适合的协议。

> 💡 **特别感谢**：酷安@低线阻狂魔、酷安@花橋桥 提供的技术方案支持

## 🚀 主要功能

- ✅ **PPS协议解锁** - 启用公版PPS快充支持
- 🔄 **文件监控** - 实时监控关键配置文件变化
- ⚙️ **状态切换** - 通过模块操作按钮快速启用/禁用PPS支持


## 🙏 致谢

- **酷安@低线阻狂魔**、**酷安@花橋桥** - 技术方案支持
- **所有测试用户** - 宝贵的反馈和建议


---

**⚡ 让每一台澎湃设备都享受自由充电体验！** 🔋

> 💝 如果这个模块对你有帮助，请给个 Star 支持一下！